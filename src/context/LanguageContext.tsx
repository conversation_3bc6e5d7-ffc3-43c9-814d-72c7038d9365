"use client";

import React, { createContext, useState, useContext, ReactNode } from 'react';

type Locale = 'en' | 'sq' | 'tr';

interface LanguageContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  translations: Record<string, Record<string, string>>;
}

const defaultTranslations = {
  en: {
    language: 'English',
    home: 'Home',
    features: 'Features',
    howItWorks: 'How It Works',
    testimonials: 'Testimonials',
    faq: 'FAQ',
    getStarted: 'Get Started',
    // Hero section
    heroTitle: 'PostCHAT - Smart Communication Platform',
    heroSubtitle: 'A modern AI-powered platform that connects all your communication channels in one place. Perfect for e-commerce stores, shipping carriers, and any business that interacts with customers.',
    learnMore: 'Learn More',
    // Features section
    featuresTitle: 'Key Features for All Businesses',
    featuresSubtitle: 'PostCHAT offers powerful features that benefit any business that communicates with customers. From e-commerce and dropshipping to shipping carriers and service providers, our platform streamlines customer interactions and automates responses.',
    unifiedCommunication: 'Unified Communication',
    unifiedCommunicationDesc: 'Connect all your communication channels in one place - Facebook, Instagram, WhatsApp, SMS, email, and more. Never miss a customer message again.',
    aiPoweredResponses: 'AI-Powered Responses',
    aiPoweredResponsesDesc: 'Our intelligent system automatically responds to common questions, order status inquiries, and product information requests without human intervention.',
    secureCustomerData: 'Secure Customer Data',
    secureCustomerDataDesc: 'All customer information and conversations are protected with enterprise-grade security. Maintain compliance with data protection regulations.',
    detailedAnalytics: 'Detailed Analytics',
    detailedAnalyticsDesc: 'Track customer interactions, response times, and satisfaction rates. Use data-driven insights to improve your communication strategy.',
    // Multichannel section
    multichannelTitle: 'Multichannel Customer Communication',
    multichannelSubtitle: 'PostCHAT connects all your communication channels in one place, whether you\'re running an e-commerce store, a shipping carrier, or any type of business that interacts with customers. Our AI-powered system automates responses, handles order inquiries, and manages customer service across all platforms.',
    forEcommerce: 'For E-commerce Stores',
    forEcommerceDesc: 'Automate order inquiries, product questions, and shipping updates across all your sales channels.',
    forCarriers: 'For Shipping Carriers',
    forCarriersDesc: 'Let customers track packages, update delivery instructions, and resolve issues through automated chat.',
    forServices: 'For Service Businesses',
    forServicesDesc: 'Handle appointment scheduling, answer FAQs, and provide instant customer support 24/7.',
  },
  sq: {
    language: 'Shqip',
    home: 'Kryefaqja',
    features: 'Veçoritë',
    howItWorks: 'Si Funksionon',
    testimonials: 'Dëshmitë',
    faq: 'Pyetje të Shpeshta',
    getStarted: 'Fillo Tani',
    // Hero section
    heroTitle: 'PostCHAT - Platforma e Komunikimit Inteligjent',
    heroSubtitle: 'Një platformë moderne e fuqizuar nga AI që lidh të gjitha kanalet tuaja të komunikimit në një vend. E përsosur për dyqanet e-commerce, transportuesit e dërgesave dhe çdo biznes që ndërvepron me klientët.',
    learnMore: 'Mëso më shumë',
    // Features section
    featuresTitle: 'Veçoritë Kryesore për të Gjitha Bizneset',
    featuresSubtitle: 'PostCHAT ofron veçori të fuqishme që përfitojnë çdo biznes që komunikon me klientët. Nga e-commerce dhe dropshipping tek transportuesit e dërgesave dhe ofruesit e shërbimeve, platforma jonë thjeshtëzon ndërveprimet me klientët dhe automatizon përgjigjet.',
    unifiedCommunication: 'Komunikim i Unifikuar',
    unifiedCommunicationDesc: 'Lidhni të gjitha kanalet tuaja të komunikimit në një vend - Facebook, Instagram, WhatsApp, SMS, email dhe më shumë. Mos humbisni kurrë një mesazh klienti.',
    aiPoweredResponses: 'Përgjigje të Fuqizuara nga AI',
    aiPoweredResponsesDesc: 'Sistemi ynë inteligjent përgjigjet automatikisht në pyetjet e zakonshme, pyetjet për statusin e porosisë dhe kërkesat për informacione produkti pa ndërhyrje njerëzore.',
    secureCustomerData: 'Të Dhëna të Sigurta të Klientëve',
    secureCustomerDataDesc: 'Të gjitha informacionet e klientëve dhe bisedat janë të mbrojtura me siguri të nivelit të ndërmarrjes. Mbani përputhshmërinë me rregulloret e mbrojtjes së të dhënave.',
    detailedAnalytics: 'Analitika të Detajuara',
    detailedAnalyticsDesc: 'Ndiqni ndërveprimet me klientët, kohët e përgjigjes dhe shkallët e kënaqësisë. Përdorni njohuri të bazuara në të dhëna për të përmirësuar strategjinë tuaj të komunikimit.',
    // Multichannel section
    multichannelTitle: 'Komunikim Shumëkanalësh me Klientët',
    multichannelSubtitle: 'PostCHAT lidh të gjitha kanalet tuaja të komunikimit në një vend, pavarësisht nëse drejtoni një dyqan e-commerce, një transportues dërgesash, ose çdo lloj biznesi që ndërvepron me klientët. Sistemi ynë i fuqizuar nga AI automatizon përgjigjet, trajton pyetjet e porosive dhe menaxhon shërbimin e klientëve në të gjitha platformat.',
    forEcommerce: 'Për Dyqanet E-commerce',
    forEcommerceDesc: 'Automatizoni pyetjet e porosive, pyetjet e produkteve dhe përditësimet e dërgesave në të gjitha kanalet tuaja të shitjes.',
    forCarriers: 'Për Transportuesit e Dërgesave',
    forCarriersDesc: 'Lejoni klientët të ndjekin paketat, të përditësojnë udhëzimet e dorëzimit dhe të zgjidhin problemet përmes chat-it të automatizuar.',
    forServices: 'Për Bizneset e Shërbimeve',
    forServicesDesc: 'Trajtoni planifikimin e takimeve, përgjigjuni pyetjeve të shpeshta dhe ofroni mbështetje të menjëhershme të klientëve 24/7.',
  },
  tr: {
    language: 'Türkçe',
    home: 'Ana Sayfa',
    features: 'Özellikler',
    howItWorks: 'Nasıl Çalışır',
    testimonials: 'Müşteri Yorumları',
    faq: 'SSS',
    getStarted: 'Hemen Başla',
    // Hero section
    heroTitle: 'PostCHAT - Akıllı İletişim Platformu',
    heroSubtitle: 'Tüm iletişim kanallarınızı tek yerde birleştiren modern, AI destekli bir platform. E-ticaret mağazaları, kargo şirketleri ve müşterilerle etkileşim kuran her türlü işletme için mükemmel.',
    learnMore: 'Daha Fazla Bilgi',
    // Features section
    featuresTitle: 'Tüm İşletmeler için Temel Özellikler',
    featuresSubtitle: 'PostCHAT, müşterilerle iletişim kuran her işletmeye fayda sağlayan güçlü özellikler sunar. E-ticaret ve dropshipping\'den kargo şirketleri ve hizmet sağlayıcılara kadar, platformumuz müşteri etkileşimlerini kolaylaştırır ve yanıtları otomatikleştirir.',
    unifiedCommunication: 'Birleşik İletişim',
    unifiedCommunicationDesc: 'Tüm iletişim kanallarınızı tek yerde birleştirin - Facebook, Instagram, WhatsApp, SMS, e-posta ve daha fazlası. Hiçbir müşteri mesajını kaçırmayın.',
    aiPoweredResponses: 'AI Destekli Yanıtlar',
    aiPoweredResponsesDesc: 'Akıllı sistemimiz, yaygın sorulara, sipariş durumu sorgularına ve ürün bilgi taleplerine insan müdahalesi olmadan otomatik olarak yanıt verir.',
    secureCustomerData: 'Güvenli Müşteri Verileri',
    secureCustomerDataDesc: 'Tüm müşteri bilgileri ve konuşmalar kurumsal düzeyde güvenlikle korunur. Veri koruma düzenlemelerine uygunluğu sürdürün.',
    detailedAnalytics: 'Detaylı Analitik',
    detailedAnalyticsDesc: 'Müşteri etkileşimlerini, yanıt sürelerini ve memnuniyet oranlarını takip edin. İletişim stratejinizi geliştirmek için veri odaklı içgörüler kullanın.',
    // Multichannel section
    multichannelTitle: 'Çok Kanallı Müşteri İletişimi',
    multichannelSubtitle: 'PostCHAT, bir e-ticaret mağazası, kargo şirketi veya müşterilerle etkileşim kuran herhangi bir işletme türü işletiyor olsanız da, tüm iletişim kanallarınızı tek yerde birleştirir. AI destekli sistemimiz yanıtları otomatikleştirir, sipariş sorgularını işler ve tüm platformlarda müşteri hizmetlerini yönetir.',
    forEcommerce: 'E-ticaret Mağazaları İçin',
    forEcommerceDesc: 'Tüm satış kanallarınızda sipariş sorgularını, ürün sorularını ve kargo güncellemelerini otomatikleştirin.',
    forCarriers: 'Kargo Şirketleri İçin',
    forCarriersDesc: 'Müşterilerin paketleri takip etmesine, teslimat talimatlarını güncellemesine ve otomatik sohbet yoluyla sorunları çözmesine olanak tanıyın.',
    forServices: 'Hizmet İşletmeleri İçin',
    forServicesDesc: 'Randevu planlamasını yönetin, SSS\'leri yanıtlayın ve 7/24 anında müşteri desteği sağlayın.',
  }
};

const LanguageContext = createContext<LanguageContextType>({
  locale: 'en',
  setLocale: () => {},
  translations: defaultTranslations
});

export const useLanguage = () => useContext(LanguageContext);

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [locale, setLocale] = useState<Locale>('en');

  const value = {
    locale,
    setLocale,
    translations: defaultTranslations
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
