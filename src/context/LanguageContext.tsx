"use client";

import React, { createContext, useState, useContext, ReactNode } from 'react';

type Locale = 'en' | 'sq' | 'tr';

interface LanguageContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  translations: Record<string, Record<string, string>>;
}

const defaultTranslations = {
  en: {
    language: 'English',
    home: 'Home',
    features: 'Features',
    howItWorks: 'How It Works',
    testimonials: 'Testimonials',
    faq: 'FAQ',
    getStarted: 'Get Started'
  },
  sq: {
    language: 'Shqip',
    home: 'Kryefaqja',
    features: 'Veçoritë',
    howItWorks: 'Si Funksionon',
    testimonials: 'Dëshmitë',
    faq: 'Pyetje të Shpeshta',
    getStarted: 'Fillo Tani'
  },
  tr: {
    language: 'Türkçe',
    home: 'Ana Sayfa',
    features: 'Özellikler',
    howItWorks: 'Nasıl Çalışır',
    testimonials: '<PERSON>üş<PERSON><PERSON> Yo<PERSON>ları',
    faq: 'SSS',
    getStarted: 'Hemen Başla'
  }
};

const LanguageContext = createContext<LanguageContextType>({
  locale: 'en',
  setLocale: () => {},
  translations: defaultTranslations
});

export const useLanguage = () => useContext(LanguageContext);

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [locale, setLocale] = useState<Locale>('en');

  const value = {
    locale,
    setLocale,
    translations: defaultTranslations
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
