"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaQuoteLeft, FaStar } from 'react-icons/fa';

const Testimonials: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(0);

  const testimonials = [
    {
      name: '<PERSON>',
      role: 'Fashion Store Owner',
      image: '/assets/images/testimonial-1.jpg',
      content: 'PostCHAT transformed my business. I went from struggling with inventory to running a successful online fashion store with zero overhead. The AI-generated social media posts save me hours every week!',
      rating: 5
    },
    {
      name: '<PERSON>',
      role: 'Tech Gadget Seller',
      image: '/assets/images/testimonial-2.jpg',
      content: 'The integration with social networks and automated ordering process has allowed me to scale my business beyond what I thought possible. I am now selling to customers worldwide with minimal effort.',
      rating: 5
    },
    {
      name: '<PERSON>',
      role: 'Home Decor Entrepreneur',
      image: '/assets/images/testimonial-3.jpg',
      content: 'As someone with no prior business experience, PostCHAT made it incredibly easy to start my dropshipping journey. The platform is intuitive, and the support team is always there when I need help.',
      rating: 4
    }
  ];

  return (
    <section id="testimonials" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">What Our Users Say</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Join thousands of satisfied entrepreneurs who have transformed their businesses with PostCHAT.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <motion.div
            key={activeIndex}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.5 }}
            className="bg-white p-8 md:p-12 rounded-2xl shadow-xl"
          >
            <div className="flex flex-col md:flex-row gap-8">
              <div className="md:w-1/3 flex flex-col items-center">
                <div className="w-24 h-24 rounded-full bg-gray-300 mb-4 overflow-hidden">
                  {/* Placeholder for testimonial image */}
                  <div className="w-full h-full bg-red-200 flex items-center justify-center text-red-600 font-bold">
                    {testimonials[activeIndex].name.split(' ').map(n => n[0]).join('')}
                  </div>
                </div>
                <h3 className="text-xl font-bold text-center">{testimonials[activeIndex].name}</h3>
                <p className="text-gray-600 text-center">{testimonials[activeIndex].role}</p>
                <div className="flex mt-2">
                  {[...Array(5)].map((_, i) => (
                    <FaStar
                      key={i}
                      className={i < testimonials[activeIndex].rating ? "text-yellow-400" : "text-gray-300"}
                    />
                  ))}
                </div>
              </div>

              <div className="md:w-2/3">
                <FaQuoteLeft className="text-4xl text-red-100 mb-4" />
                <p className="text-lg text-gray-700 italic mb-6">
                  {testimonials[activeIndex].content}
                </p>
              </div>
            </div>
          </motion.div>

          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setActiveIndex(index)}
                className={`w-3 h-3 rounded-full transition-colors duration-300 ${
                  activeIndex === index ? 'bg-red-600' : 'bg-gray-300'
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>

        <div className="mt-16 bg-red-600 text-white rounded-xl p-8 md:p-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold mb-2">5,000+</div>
              <p>Active Businesses</p>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">145,000+</div>
              <p>End Customers</p>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">5M+</div>
              <p>Social Media Reach</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
