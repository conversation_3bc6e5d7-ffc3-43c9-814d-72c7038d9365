import React from 'react';
import { motion } from 'framer-motion';
import { FaSearch, FaList, FaShoppingCart, FaEdit, FaGlobe, FaChartBar } from 'react-icons/fa';

const HowItWorks: React.FC = () => {
  const steps = [
    {
      icon: <FaSearch className="text-3xl text-red-600" />,
      title: 'Selecting Products',
      description: 'Enter the platform, go to the Dropshipping module, and identify categories that fit your target market.'
    },
    {
      icon: <FaList className="text-3xl text-red-600" />,
      title: 'Browse Products',
      description: 'Explore thousands of products from various suppliers with clear technical specifications, prices, and delivery terms.'
    },
    {
      icon: <FaShoppingCart className="text-3xl text-red-600" />,
      title: 'Choose for Sale',
      description: 'Click "I want to sell" and the product is automatically added to your virtual store\'s inventory.'
    },
    {
      icon: <FaEdit className="text-3xl text-red-600" />,
      title: 'Customize Details',
      description: 'Modify the description, price, and labels according to your marketing strategy and target audience.'
    },
    {
      icon: <FaGlobe className="text-3xl text-red-600" />,
      title: 'Publish in Store',
      description: 'Press the "Publish" button and the product becomes available to your customers across all sales channels.'
    },
    {
      icon: <FaChartBar className="text-3xl text-red-600" />,
      title: 'Monitor Performance',
      description: 'Follow sales statistics and customer feedback to optimize your offerings over time.'
    }
  ];

  return (
    <section id="how-it-works" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Dropshipping Operations and Sales</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our platform simplifies the entire process from product selection to sales monitoring,
            making it easy for you to run a successful dropshipping business.
          </p>
        </div>

        <div className="relative">
          {/* Connecting line */}
          <div className="absolute left-1/2 top-0 bottom-0 w-1 bg-red-100 hidden md:block transform -translate-x-1/2"></div>
          
          <div className="space-y-12">
            {steps.map((step, index) => (
              <motion.div 
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.2 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className={`flex flex-col ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'} items-center gap-8`}
              >
                <div className={`md:w-1/2 ${index % 2 === 0 ? 'md:text-right' : 'md:text-left'}`}>
                  <h3 className="text-2xl font-bold mb-3">{step.title}</h3>
                  <p className="text-gray-600">{step.description}</p>
                </div>
                
                <div className="relative z-10 flex items-center justify-center w-16 h-16 bg-white rounded-full border-4 border-red-100 shadow-lg">
                  {step.icon}
                </div>
                
                <div className="md:w-1/2"></div>
              </motion.div>
            ))}
          </div>
        </div>

        <div className="mt-20 text-center">
          <h3 className="text-2xl font-bold mb-4">Ready to Start Your Dropshipping Business?</h3>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto mb-8">
            Join thousands of successful entrepreneurs who are already using PostCHAT to build
            profitable online businesses without the hassle of inventory management.
          </p>
          <a 
            href="#contact" 
            className="inline-block bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-full font-medium transition-colors duration-300"
          >
            Get Started Today
          </a>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
