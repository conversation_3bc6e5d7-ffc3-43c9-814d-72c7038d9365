"use client";

import React from 'react';
import Header from "./Header";
import <PERSON> from "./Hero";
import Features from "./Features";
import HowItWorks from "./HowItWorks";
import AIFeatures from "./AIFeatures";
import Multichannel from "./Multichannel";
import Tracking from "./Tracking";
import Security from "./Security";
import Testimonials from "./Testimonials";
import FAQ from "./FAQ";
import CTA from "./CTA";
import Footer from "./Footer";

const ClientWrapper: React.FC = () => {
  return (
    <>
      <Header />
      <main>
        <Hero />
        <Multichannel />
        <Features />
        <HowItWorks />
        <AIFeatures />
        <Tracking />
        <Security />
        <Testimonials />
        <FAQ />
        <CTA />
      </main>
      <Footer />
    </>
  );
};

export default ClientWrapper;
