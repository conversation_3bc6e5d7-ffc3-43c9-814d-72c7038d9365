import React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { FaArrowRight } from 'react-icons/fa';

const Hero: React.FC = () => {
  return (
    <section id="home" className="pt-32 pb-20 md:pt-40 md:pb-24 bg-gradient-to-br from-red-600 to-red-800 text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-20 -left-20 w-80 h-80 bg-red-500 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute top-1/2 -right-20 w-80 h-80 bg-red-400 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute -bottom-20 left-1/3 w-80 h-80 bg-red-300 rounded-full opacity-20 blur-3xl"></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
              PostCHAT - Smart Sales Platform
            </h1>
            <p className="text-xl text-red-100 mb-8">
              A modern platform for borderless, stock-free, and staff-free sales. 
              Built on the concept of dropshipping and the empowerment of social networks.
            </p>
            <div className="flex flex-wrap gap-4">
              <a 
                href="#features" 
                className="bg-white text-red-600 hover:bg-red-100 px-8 py-3 rounded-full font-medium flex items-center transition-colors duration-300"
              >
                Learn More <FaArrowRight className="ml-2" />
              </a>
              <a 
                href="#contact" 
                className="bg-transparent hover:bg-red-700 border-2 border-white text-white px-8 py-3 rounded-full font-medium transition-colors duration-300"
              >
                Get Started
              </a>
            </div>
            
            <div className="mt-12 flex items-center">
              <div className="flex -space-x-2">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="w-10 h-10 rounded-full bg-red-400 border-2 border-white flex items-center justify-center text-xs font-bold">
                    {i}
                  </div>
                ))}
              </div>
              <p className="ml-4 text-red-100">
                Over <span className="font-bold">5,000</span> entrepreneurs are already benefiting
              </p>
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="hidden lg:block"
          >
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-red-400 to-red-600 rounded-xl blur-xl opacity-30 transform -rotate-6"></div>
              <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 p-6 rounded-xl shadow-2xl">
                <div className="aspect-[4/3] bg-gradient-to-br from-red-500 to-red-700 rounded-lg overflow-hidden relative">
                  <div className="absolute inset-0 flex items-center justify-center text-white text-xl font-bold">
                    PostCHAT Dashboard Preview
                  </div>
                </div>
                <div className="mt-4 grid grid-cols-2 gap-4">
                  <div className="bg-white/10 p-3 rounded-lg">
                    <div className="text-3xl font-bold">5K+</div>
                    <div className="text-sm text-red-100">Active Users</div>
                  </div>
                  <div className="bg-white/10 p-3 rounded-lg">
                    <div className="text-3xl font-bold">145K+</div>
                    <div className="text-sm text-red-100">End Customers</div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
