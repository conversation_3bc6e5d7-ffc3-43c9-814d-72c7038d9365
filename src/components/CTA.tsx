"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { FaArrowRight, FaAndroid } from 'react-icons/fa';

const CTA: React.FC = () => {
  return (
    <section id="contact" className="py-20 bg-gradient-to-br from-red-600 to-red-800 text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-20 -right-20 w-80 h-80 bg-red-500 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute -bottom-20 left-1/3 w-80 h-80 bg-red-400 rounded-full opacity-20 blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Join PostCHAT Today</h2>
            <p className="text-xl text-red-100 mb-8">
              Our innovative platform offers endless possibilities to expand your business and reach customers worldwide.
              Start your journey towards financial independence today!
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="bg-white/10 backdrop-blur-sm p-6 rounded-xl">
                <h3 className="text-xl font-bold mb-3">Sell from Anywhere</h3>
                <p className="text-red-100 mb-4">
                  Without geographical limitations, you can expand your business into new international markets.
                </p>
                <ul className="text-red-100 space-y-2">
                  <li className="flex items-start">
                    <FaArrowRight className="mt-1 mr-2 text-red-300" />
                    <span>Enter new markets without additional investments</span>
                  </li>
                  <li className="flex items-start">
                    <FaArrowRight className="mt-1 mr-2 text-red-300" />
                    <span>Achieve 24/7 sales across all time zones</span>
                  </li>
                </ul>
              </div>

              <div className="bg-white/10 backdrop-blur-sm p-6 rounded-xl">
                <h3 className="text-xl font-bold mb-3">Ship from Anywhere</h3>
                <p className="text-red-100 mb-4">
                  Our global network of suppliers allows you to ship products to any corner of the world at competitive costs.
                </p>
                <ul className="text-red-100 space-y-2">
                  <li className="flex items-start">
                    <FaArrowRight className="mt-1 mr-2 text-red-300" />
                    <span>Direct integration with international carriers</span>
                  </li>
                  <li className="flex items-start">
                    <FaArrowRight className="mt-1 mr-2 text-red-300" />
                    <span>Real-time shipment tracking</span>
                  </li>
                </ul>
              </div>

              <div className="bg-white/10 backdrop-blur-sm p-6 rounded-xl">
                <h3 className="text-xl font-bold mb-3">Profit from Everything</h3>
                <p className="text-red-100 mb-4">
                  Modern solutions for smart selling that maximize your profits through optimized pricing and market analysis.
                </p>
                <ul className="text-red-100 space-y-2">
                  <li className="flex items-start">
                    <FaArrowRight className="mt-1 mr-2 text-red-300" />
                    <span>Detailed financial reporting</span>
                  </li>
                  <li className="flex items-start">
                    <FaArrowRight className="mt-1 mr-2 text-red-300" />
                    <span>Intelligent price suggestions</span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="flex flex-wrap justify-center gap-4">
              <a
                href="#"
                className="bg-white text-red-600 hover:bg-red-100 px-8 py-3 rounded-full font-medium flex items-center transition-colors duration-300"
              >
                Get Started Now <FaArrowRight className="ml-2" />
              </a>
              <a
                href="#"
                className="bg-transparent hover:bg-red-700 border-2 border-white text-white px-8 py-3 rounded-full font-medium flex items-center transition-colors duration-300"
              >
                <FaAndroid className="mr-2" /> Download App
                <span className="ml-2 text-xs bg-yellow-400 text-red-800 px-2 py-1 rounded-full">Prototype</span>
              </a>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default CTA;
