"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { FaShieldAlt, FaCheckCircle, FaChartBar, FaLock, FaUserShield, FaCreditCard } from 'react-icons/fa';

const Security: React.FC = () => {
  const features = [
    {
      icon: <FaShieldAlt className="text-4xl text-red-600" />,
      title: "Maximum Security",
      description: "Data and payment protection with advanced encryption. Our system uses the latest security protocols to ensure that your data remains protected from any cyber threats."
    },
    {
      icon: <FaCheckCircle className="text-4xl text-red-600" />,
      title: "Quality Control",
      description: "Evaluation of suppliers and postal services through advanced methodologies. We continuously monitor the performance of our partners to ensure the highest service standards for your customers."
    },
    {
      icon: <FaChartBar className="text-4xl text-red-600" />,
      title: "Comprehensive Reporting",
      description: "Informing all parties with detailed and real-time reports. Generate analytical reports on business performance, customer behavior, and the efficiency of your logistics processes."
    }
  ];

  const transparencyFeatures = [
    {
      icon: <FaLock className="text-3xl text-white" />,
      title: "Traceability",
      description: "Every order is traceable from start to finish. Our advanced system allows you to track the location of your products in real-time, ensuring complete clarity throughout the delivery process."
    },
    {
      icon: <FaUserShield className="text-3xl text-white" />,
      title: "Notification",
      description: "All parties are automatically informed of any status changes. Instant notifications are sent for order confirmation, processing, shipping, and delivery, eliminating uncertainty and providing peace of mind."
    },
    {
      icon: <FaCreditCard className="text-3xl text-white" />,
      title: "Protection",
      description: "Protected data and payments with the latest security technologies. We use advanced encryption and multi-factor verification systems to ensure your personal information and financial transactions remain completely secure."
    }
  ];

  return (
    <section id="security" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Reporting and Security</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our platform provides integrated solutions for data security and detailed reporting. 
            These functionalities are essential for your business and ensure maximum reliability in all processes.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100"
            >
              <div className="mb-6 flex justify-center">
                <div className="bg-red-50 p-4 rounded-full">
                  {feature.icon}
                </div>
              </div>
              <h3 className="text-xl font-bold mb-3 text-center">{feature.title}</h3>
              <p className="text-gray-600 text-center">{feature.description}</p>
            </motion.div>
          ))}
        </div>
        
        <div className="text-center mb-12">
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            With these three key components, our platform creates a secure and transparent ecosystem for all your e-commerce operations. 
            This allows you to focus on growing your business while we take care of data security and reporting.
          </p>
        </div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="bg-gradient-to-r from-red-600 to-red-800 rounded-xl p-8 text-white"
        >
          <h3 className="text-2xl font-bold mb-6 text-center">Transparency and Trust Guarantee</h3>
          <p className="text-center text-red-100 mb-8 max-w-3xl mx-auto">
            Our platform provides secure and transparent solutions for all users, ensuring trust at every step of the process.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {transparencyFeatures.map((feature, index) => (
              <div key={index} className="bg-white/10 backdrop-blur-sm p-6 rounded-lg">
                <div className="bg-red-700 w-12 h-12 rounded-full flex items-center justify-center mb-4 mx-auto">
                  {feature.icon}
                </div>
                <h4 className="text-xl font-bold mb-2 text-center">{feature.title}</h4>
                <p className="text-red-100 text-sm text-center">{feature.description}</p>
              </div>
            ))}
          </div>
          
          <p className="text-center mt-8 text-red-100">
            We continuously invest in improving our security and transparency systems to provide you with the most reliable service in the market. 
            Our solution is designed with the user in mind, combining ease of use with the highest security standards.
          </p>
        </motion.div>
        
        <div className="mt-20">
          <div className="bg-white p-8 rounded-xl shadow-lg border border-gray-100">
            <div className="flex flex-col md:flex-row items-center gap-8">
              <div className="md:w-1/3">
                <div className="relative">
                  <div className="absolute inset-0 bg-red-100 rounded-full blur-xl opacity-50"></div>
                  <div className="relative bg-gradient-to-r from-red-500 to-red-700 p-8 rounded-xl text-white text-center">
                    <FaShieldAlt className="text-6xl mx-auto mb-4" />
                    <h4 className="text-xl font-bold">Security First</h4>
                    <p className="mt-2 text-red-100">Your data and transactions are our top priority</p>
                  </div>
                </div>
              </div>
              
              <div className="md:w-2/3">
                <h3 className="text-2xl font-bold mb-4">Enterprise-Grade Security</h3>
                <p className="text-gray-600 mb-6">
                  PostCHAT implements the highest security standards to protect your business data and customer information. 
                  Our platform is regularly audited by independent security experts to ensure compliance with international regulations.
                </p>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="flex items-start">
                    <FaCheckCircle className="text-green-500 mt-1 mr-2" />
                    <div>
                      <h5 className="font-bold">End-to-End Encryption</h5>
                      <p className="text-sm text-gray-600">All data is encrypted in transit and at rest</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <FaCheckCircle className="text-green-500 mt-1 mr-2" />
                    <div>
                      <h5 className="font-bold">PCI DSS Compliance</h5>
                      <p className="text-sm text-gray-600">Secure payment processing standards</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <FaCheckCircle className="text-green-500 mt-1 mr-2" />
                    <div>
                      <h5 className="font-bold">GDPR Compliance</h5>
                      <p className="text-sm text-gray-600">Full data protection and privacy controls</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <FaCheckCircle className="text-green-500 mt-1 mr-2" />
                    <div>
                      <h5 className="font-bold">24/7 Monitoring</h5>
                      <p className="text-sm text-gray-600">Continuous security surveillance</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Security;
