"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { FaBox, FaUsers, FaShieldAlt, FaChartLine } from 'react-icons/fa';
import { useLanguage } from '@/context/LanguageContext';

const Features: React.FC = () => {
  const { locale, translations } = useLanguage();

  const features = [
    {
      icon: <FaUsers className="text-4xl text-red-600" />,
      title: translations[locale].unifiedCommunication,
      description: translations[locale].unifiedCommunicationDesc,
    },
    {
      icon: <FaBox className="text-4xl text-red-600" />,
      title: translations[locale].aiPoweredResponses,
      description: translations[locale].aiPoweredResponsesDesc,
    },
    {
      icon: <FaShieldAlt className="text-4xl text-red-600" />,
      title: translations[locale].secureCustomerData,
      description: translations[locale].secureCustomerDataDesc,
    },
    {
      icon: <FaChartLine className="text-4xl text-red-600" />,
      title: translations[locale].detailedAnalytics,
      description: translations[locale].detailedAnalyticsDesc,
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <section id="features" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">{translations[locale].featuresTitle}</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {translations[locale].featuresSubtitle}
          </p>
        </div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"
              variants={itemVariants}
            >
              <div className="mb-6">{feature.icon}</div>
              <h3 className="text-xl font-bold mb-3">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </motion.div>
          ))}
        </motion.div>

        <div className="mt-16 text-center">
          <p className="text-lg text-gray-700 max-w-4xl mx-auto mb-8">
            PostCHAT adapts to your business needs, whether you're running an e-commerce store,
            a shipping carrier service, or any business that values customer communication.
            Our platform scales with your growth and integrates seamlessly with your existing systems.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-10">
            <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100">
              <h3 className="font-bold text-lg mb-2 text-red-600">For E-commerce</h3>
              <ul className="text-sm text-left text-gray-600 space-y-2">
                <li>• Automated order status updates</li>
                <li>• Product recommendations</li>
                <li>• Inventory availability checks</li>
              </ul>
            </div>

            <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100">
              <h3 className="font-bold text-lg mb-2 text-red-600">For Shipping Carriers</h3>
              <ul className="text-sm text-left text-gray-600 space-y-2">
                <li>• Real-time delivery updates</li>
                <li>• Address change requests</li>
                <li>• Delivery instructions</li>
              </ul>
            </div>

            <div className="bg-white p-5 rounded-lg shadow-md border border-gray-100">
              <h3 className="font-bold text-lg mb-2 text-red-600">For Service Businesses</h3>
              <ul className="text-sm text-left text-gray-600 space-y-2">
                <li>• Appointment scheduling</li>
                <li>• Service inquiries</li>
                <li>• Follow-up reminders</li>
              </ul>
            </div>
          </div>

          <a
            href="#how-it-works"
            className="inline-block bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-full font-medium transition-colors duration-300"
          >
            See How It Works
          </a>
        </div>
      </div>
    </section>
  );
};

export default Features;
