"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { FaBox, FaUsers, FaShieldAlt, FaChartLine } from 'react-icons/fa';

const Features: React.FC = () => {
  const features = [
    {
      icon: <FaBox className="text-4xl text-red-600" />,
      title: 'Zero Inventory',
      description: 'You do not physically hold merchandise in a warehouse. Your suppliers store, package, and ship the products directly to your customers.',
    },
    {
      icon: <FaUsers className="text-4xl text-red-600" />,
      title: 'Zero Staff',
      description: 'The model operates without the need for additional employees. The automated platform and suppliers handle most of the processes.',
    },
    {
      icon: <FaShieldAlt className="text-4xl text-red-600" />,
      title: 'Zero Risk',
      description: 'Dropshipping eliminates the risk of unsold merchandise. You only purchase products after your customers have ordered and paid for them.',
    },
    {
      icon: <FaChartLine className="text-4xl text-red-600" />,
      title: 'Guaranteed Profit',
      description: 'You benefit from the price difference. You buy at a lower price from the supplier and sell at a higher price to your customer.',
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <section id="features" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">What is Dropshipping?</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Dropshipping is a modern business model that allows you to sell products
            online without the need to maintain inventory. You simply connect customers
            with suppliers and profit from the intermediation.
          </p>
        </div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"
              variants={itemVariants}
            >
              <div className="mb-6">{feature.icon}</div>
              <h3 className="text-xl font-bold mb-3">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </motion.div>
          ))}
        </motion.div>

        <div className="mt-16 text-center">
          <p className="text-lg text-gray-700 max-w-4xl mx-auto mb-8">
            With dropshipping, you can start an online business with minimal investment
            and quickly expand into different global markets, offering hundreds of products
            without logistical complications.
          </p>
          <a
            href="#how-it-works"
            className="inline-block bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-full font-medium transition-colors duration-300"
          >
            See How It Works
          </a>
        </div>
      </div>
    </section>
  );
};

export default Features;
