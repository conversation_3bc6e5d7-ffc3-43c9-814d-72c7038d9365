"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaChevronDown, FaChevronUp } from 'react-icons/fa';

const FAQ: React.FC = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = [
    {
      question: 'What is PostCHAT and how does it work?',
      answer: 'PostCHAT is a smart sales platform that connects entrepreneurs with suppliers through dropshipping. It allows you to sell products online without holding inventory. The platform automates the ordering and shipping process, integrates with social networks for targeted marketing, and provides data analysis tools for sales forecasting.'
    },
    {
      question: 'Do I need any prior business experience to use PostCHAT?',
      answer: 'No, PostCHAT is designed to be user-friendly for entrepreneurs at all experience levels. The platform provides intuitive tools and guidance to help you set up and run your online business, even if you are just starting out.'
    },
    {
      question: 'How much does it cost to start selling with PostCHAT?',
      answer: 'PostCHAT offers flexible pricing plans to accommodate businesses of different sizes. You can start with a basic plan and upgrade as your business grows. Contact our sales team for detailed pricing information tailored to your specific needs.'
    },
    {
      question: 'How do I handle customer service and returns?',
      answer: 'PostCHAT provides integrated customer service tools to help you manage inquiries and issues. For returns, the platform coordinates with suppliers to ensure a smooth process. You will have access to automated response systems and detailed tracking information to keep your customers informed.'
    },
    {
      question: 'Can I sell internationally with PostCHAT?',
      answer: 'Yes, PostCHAT supports international sales. The platform integrates with global shipping providers and offers multi-language support to help you reach customers worldwide. You can customize your shipping options and pricing based on different regions.'
    },
    {
      question: 'How does the AI-generated social media content work?',
      answer: 'The PostCHAT AI system automatically generates professional social media posts for your products. It creates engaging designs and persuasive text optimized for different platforms. You can schedule posts for maximum engagement and track their performance through detailed analytics.'
    }
  ];

  const toggleQuestion = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section id="faq" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Frequently Asked Questions</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Find answers to common questions about PostCHAT and how it can help you build a successful online business.
          </p>
        </div>

        <div className="max-w-3xl mx-auto">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="mb-4 border-b border-gray-200 pb-4"
            >
              <button
                className="flex justify-between items-center w-full text-left py-4 focus:outline-none"
                onClick={() => toggleQuestion(index)}
              >
                <h3 className="text-xl font-semibold">{faq.question}</h3>
                {openIndex === index ? (
                  <FaChevronUp className="text-red-600" />
                ) : (
                  <FaChevronDown className="text-red-600" />
                )}
              </button>

              <AnimatePresence>
                {openIndex === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <p className="text-gray-600 pb-4">{faq.answer}</p>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-gray-600 mb-6">
            Still have questions? We are here to help!
          </p>
          <a
            href="#contact"
            className="inline-block bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-full font-medium transition-colors duration-300"
          >
            Contact Us
          </a>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
