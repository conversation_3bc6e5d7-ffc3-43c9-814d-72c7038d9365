"use client";

import React, { useState } from 'react';
import { FaGlobe, FaChevronDown } from 'react-icons/fa';
import { useLanguage } from '@/context/LanguageContext';

// Available languages
const locales = ['en', 'sq', 'tr'];

const LanguageSelector: React.FC = () => {
  const { locale, setLocale, translations } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);

  const handleLanguageChange = (newLocale: string) => {
    setLocale(newLocale as any);
    setIsOpen(false);
    document.documentElement.lang = newLocale;
  };

  return (
    <div className="relative">
      <button
        className="flex items-center space-x-1 text-sm font-medium bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full hover:bg-white/20 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <FaGlobe className="mr-1" />
        <span>{translations[locale].language}</span>
        <FaChevronDown className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
          <div className="py-1" role="menu" aria-orientation="vertical">
            {locales.map((langCode) => (
              <button
                key={langCode}
                className={`block w-full text-left px-4 py-2 text-sm ${
                  langCode === locale ? 'bg-red-50 text-red-600 font-medium' : 'text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => handleLanguageChange(langCode)}
                role="menuitem"
              >
                {translations[langCode].language}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LanguageSelector;
